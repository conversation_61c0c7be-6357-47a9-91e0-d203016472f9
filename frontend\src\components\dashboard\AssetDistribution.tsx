import React from 'react';
import Card from '../ui/Card';
import { mockTokens } from '../../data/mockData';

const AssetDistribution: React.FC = () => {
  const totalValue = mockTokens.reduce((total, token) => total + token.value, 0);
  
  // Calculate percentage for each token
  const tokenWithPercentage = mockTokens.map(token => ({
    ...token,
    percentage: (token.value / totalValue) * 100
  }));
  
  // Sort tokens by value (largest first)
  const sortedTokens = [...tokenWithPercentage].sort((a, b) => b.value - a.value);
  
  return (
    <Card title="Asset Distribution" className="mb-6">
      <div className="p-4">
        <div className="mb-4 flex h-4 overflow-hidden rounded-full bg-gray-200 dark:bg-gray-700">
          {sortedTokens.map((token, index) => (
            <div
              key={token.id}
              className="h-full"
              style={{
                width: `${token.percentage}%`,
                backgroundColor: getColorByIndex(index),
              }}
            ></div>
          ))}
        </div>
        
        <div className="space-y-3">
          {sortedTokens.map((token, index) => (
            <div key={token.id} className="flex items-center justify-between">
              <div className="flex items-center">
                <div
                  className="h-3 w-3 rounded-full mr-2"
                  style={{ backgroundColor: getColorByIndex(index) }}
                ></div>
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {token.name} ({token.symbol})
                </span>
              </div>
              <div className="flex items-center">
                <span className="text-sm text-gray-500 dark:text-gray-400 mr-2">
                  ${token.value.toLocaleString()}
                </span>
                <span className="text-xs font-medium text-gray-400 dark:text-gray-500 w-12 text-right">
                  {token.percentage.toFixed(1)}%
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </Card>
  );
};

// Function to get a color based on the index
function getColorByIndex(index: number): string {
  const colors = [
    '#7A5AF8', // Indigo
    '#2DD4BF', // Teal
    '#F97316', // Orange
    '#10B981', // Green
    '#EF4444', // Red
    '#8B5CF6', // Purple
    '#F59E0B', // Amber
    '#06B6D4'  // Cyan
  ];
  
  return colors[index % colors.length];
}

export default AssetDistribution;