import React from 'react';
import Card from '../ui/Card';
import { ArrowUp, ArrowDown, DollarSign, TrendingUp } from 'lucide-react';
import { mockUser } from '../../data/mockData';

const StatItem: React.FC<{
  title: string;
  value: string;
  change?: number;
  icon: React.ReactNode;
}> = ({ title, value, change, icon }) => {
  return (
    <div className="flex items-start p-4">
      <div className="rounded-lg p-2 bg-indigo-100 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-400 mr-4">
        {icon}
      </div>
      <div>
        <p className="text-sm font-medium text-gray-500 dark:text-gray-400">{title}</p>
        <p className="text-xl font-semibold mt-1 text-gray-900 dark:text-white">{value}</p>
        {change !== undefined && (
          <div className="flex items-center mt-1">
            {change > 0 ? (
              <ArrowUp className="h-3 w-3 text-green-500 mr-1" />
            ) : (
              <ArrowDown className="h-3 w-3 text-red-500 mr-1" />
            )}
            <span className={`text-xs font-medium ${change > 0 ? 'text-green-500' : 'text-red-500'}`}>
              {Math.abs(change).toFixed(2)}%
            </span>
          </div>
        )}
      </div>
    </div>
  );
};

const PortfolioSummary: React.FC = () => {
  return (
    <Card className="mb-6">
      <div className="grid grid-cols-1 md:grid-cols-3 divide-y md:divide-y-0 md:divide-x divide-gray-200 dark:divide-gray-700">
        <StatItem
          title="Total Portfolio Value"
          value={`$${mockUser.portfolioValue.toLocaleString()}`}
          icon={<DollarSign className="h-5 w-5" />}
        />
        <StatItem
          title="24h Change"
          value={`$${(mockUser.portfolioValue * mockUser.portfolioChange24h / 100).toLocaleString()}`}
          change={mockUser.portfolioChange24h}
          icon={<TrendingUp className="h-5 w-5" />}
        />
        <StatItem
          title="7d Change"
          value={`$${(mockUser.portfolioValue * mockUser.portfolioChange7d / 100).toLocaleString()}`}
          change={mockUser.portfolioChange7d}
          icon={<TrendingUp className="h-5 w-5" />}
        />
      </div>
    </Card>
  );
};

export default PortfolioSummary;