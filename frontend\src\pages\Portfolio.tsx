import React, { useState } from 'react';
import Price<PERSON>hart from '../components/portfolio/PriceChart';
import TokenList from '../components/dashboard/TokenList';
import Card from '../components/ui/Card';
import Button from '../components/ui/Button';
import { Plus, Wallet, TrendingUp, <PERSON>Chart3, Pie<PERSON><PERSON> } from 'lucide-react';
import { mockTokens } from '../data/mockData';

const Portfolio: React.FC = () => {
  const [selectedToken, setSelectedToken] = useState(mockTokens[0]);
  const [viewMode, setViewMode] = useState<'list' | 'chart'>('list');
  
  return (
    <div>
      <div className="flex flex-col md:flex-row md:items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">Portfolio</h1>
          <p className="text-gray-600 dark:text-gray-400">Manage and track your crypto assets</p>
        </div>
        
        <div className="flex items-center space-x-4 mt-4 md:mt-0">
          <div className="flex bg-gray-100 dark:bg-gray-800 p-1 rounded-md">
            <button
              onClick={() => setViewMode('list')}
              className={`flex items-center px-3 py-1.5 rounded-md text-sm font-medium ${
                viewMode === 'list'
                  ? 'bg-white dark:bg-gray-700 text-gray-800 dark:text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-300'
              }`}
            >
              <BarChart3 className="h-4 w-4 mr-1" />
              List
            </button>
            <button
              onClick={() => setViewMode('chart')}
              className={`flex items-center px-3 py-1.5 rounded-md text-sm font-medium ${
                viewMode === 'chart'
                  ? 'bg-white dark:bg-gray-700 text-gray-800 dark:text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-300'
              }`}
            >
              <PieChart className="h-4 w-4 mr-1" />
              Charts
            </button>
          </div>
          
          <Button variant="primary" size="sm">
            <Plus className="h-4 w-4 mr-2" />
            Add Asset
          </Button>
        </div>
      </div>
      
      {viewMode === 'list' ? (
        <TokenList />
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <PriceChart token={selectedToken} />
            
            <Card>
              <div className="p-5">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  Token Details
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                    <div className="flex items-start">
                      <Wallet className="h-5 w-5 text-indigo-500 dark:text-indigo-400 mt-0.5 mr-3" />
                      <div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">Holdings</div>
                        <div className="text-lg font-medium text-gray-900 dark:text-white mt-1">
                          {selectedToken.amount} {selectedToken.symbol}
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                    <div className="flex items-start">
                      <TrendingUp className="h-5 w-5 text-indigo-500 dark:text-indigo-400 mt-0.5 mr-3" />
                      <div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">Value</div>
                        <div className="text-lg font-medium text-gray-900 dark:text-white mt-1">
                          ${selectedToken.value.toLocaleString()}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          </div>
          
          <div>
            <Card title="Your Assets">
              <div className="divide-y divide-gray-200 dark:divide-gray-700">
                {mockTokens.map((token) => (
                  <div 
                    key={token.id}
                    onClick={() => setSelectedToken(token)}
                    className={`p-4 cursor-pointer transition-colors ${
                      selectedToken.id === token.id
                        ? 'bg-indigo-50 dark:bg-indigo-900/10'
                        : 'hover:bg-gray-50 dark:hover:bg-gray-800/30'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <img 
                          src={token.logo} 
                          alt={token.name} 
                          className="h-8 w-8 rounded-full bg-gray-200 dark:bg-gray-700 p-1"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.src = 'https://placehold.co/200x200/9333ea/ffffff?text=' + token.symbol;
                          }}
                        />
                        <div className="ml-3">
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {token.name}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            {token.symbol}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          ${token.value.toLocaleString()}
                        </div>
                        <div className={`text-xs ${token.change24h >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                          {token.change24h >= 0 ? '+' : ''}{token.change24h}%
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          </div>
        </div>
      )}
    </div>
  );
};

export default Portfolio;