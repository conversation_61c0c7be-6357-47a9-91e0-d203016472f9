import React, { useState } from 'react';
import Card from '../components/ui/Card';
import Button from '../components/ui/Button';
import AlertItem from '../components/alerts/AlertItem';
import { Bell, Plus, Filter } from 'lucide-react';
import { mockAlerts } from '../data/mockData';

const Alerts: React.FC = () => {
  const [alerts, setAlerts] = useState(mockAlerts);
  const [typeFilter, setTypeFilter] = useState<string>('all');
  
  const markAsRead = (id: string) => {
    setAlerts(
      alerts.map(alert => 
        alert.id === id ? { ...alert, read: true } : alert
      )
    );
  };
  
  const markAllAsRead = () => {
    setAlerts(
      alerts.map(alert => ({ ...alert, read: true }))
    );
  };
  
  const filteredAlerts = alerts.filter(alert => {
    if (typeFilter !== 'all' && alert.type !== typeFilter) {
      return false;
    }
    return true;
  });
  
  return (
    <div>
      <div className="flex flex-col md:flex-row md:items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">Alerts</h1>
          <p className="text-gray-600 dark:text-gray-400">Manage your notifications and alerts</p>
        </div>
        
        <div className="flex items-center space-x-4 mt-4 md:mt-0">
          <select
            value={typeFilter}
            onChange={(e) => setTypeFilter(e.target.value)}
            className="form-select rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 focus:ring-indigo-500 focus:border-indigo-500 text-sm"
          >
            <option value="all">All Alerts</option>
            <option value="price">Price Alerts</option>
            <option value="airdrop">Airdrop Alerts</option>
            <option value="news">News Alerts</option>
          </select>
          
          <Button variant="outline" size="sm" onClick={markAllAsRead}>
            Mark All Read
          </Button>
          
          <Button variant="primary" size="sm">
            <Plus className="h-4 w-4 mr-2" />
            Create Alert
          </Button>
        </div>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <Card>
            {filteredAlerts.length > 0 ? (
              <div className="divide-y divide-gray-200 dark:divide-gray-700">
                {filteredAlerts.map(alert => (
                  <AlertItem 
                    key={alert.id} 
                    alert={alert} 
                    onMarkAsRead={markAsRead} 
                  />
                ))}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-12 text-center">
                <div className="bg-gray-100 dark:bg-gray-800 rounded-full p-4 mb-4">
                  <Bell className="h-8 w-8 text-gray-500 dark:text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-1">No alerts found</h3>
                <p className="text-gray-500 dark:text-gray-400 max-w-md">
                  You don't have any alerts matching your filters. Try adjusting your filters or create a new alert.
                </p>
                <Button variant="primary" size="sm" className="mt-4">
                  <Plus className="h-4 w-4 mr-2" />
                  Create Alert
                </Button>
              </div>
            )}
          </Card>
        </div>
        
        <div>
          <Card title="Create Alert">
            <div className="p-5">
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Alert Type
                </label>
                <select className="w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 focus:ring-indigo-500 focus:border-indigo-500">
                  <option value="price">Price Alert</option>
                  <option value="airdrop">Airdrop Alert</option>
                  <option value="news">News Alert</option>
                </select>
              </div>
              
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Asset
                </label>
                <select className="w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 focus:ring-indigo-500 focus:border-indigo-500">
                  <option value="">Select an asset</option>
                  <option value="btc">Bitcoin (BTC)</option>
                  <option value="eth">Ethereum (ETH)</option>
                  <option value="sol">Solana (SOL)</option>
                </select>
              </div>
              
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Condition
                </label>
                <div className="flex space-x-2">
                  <select className="w-1/3 rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 focus:ring-indigo-500 focus:border-indigo-500">
                    <option value="above">Above</option>
                    <option value="below">Below</option>
                  </select>
                  <input 
                    type="number" 
                    placeholder="Enter price" 
                    className="w-2/3 rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 focus:ring-indigo-500 focus:border-indigo-500"
                  />
                </div>
              </div>
              
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Priority
                </label>
                <select className="w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 focus:ring-indigo-500 focus:border-indigo-500">
                  <option value="low">Low</option>
                  <option value="medium">Medium</option>
                  <option value="high">High</option>
                </select>
              </div>
              
              <Button variant="primary" fullWidth>
                Create Alert
              </Button>
            </div>
          </Card>
          
          <Card title="Alert Settings" className="mt-6">
            <div className="p-5 space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-900 dark:text-white">Email Notifications</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">Receive alerts via email</p>
                </div>
                <div className="relative inline-block w-10 mr-2 align-middle select-none">
                  <input 
                    type="checkbox" 
                    id="toggle-1" 
                    className="sr-only"
                    defaultChecked
                  />
                  <label 
                    htmlFor="toggle-1" 
                    className="block h-6 w-10 rounded-full bg-gray-300 dark:bg-gray-600 cursor-pointer"
                  ></label>
                  <span 
                    className="absolute left-0 top-0 block h-6 w-6 transform translate-x-0 rounded-full bg-white border border-gray-300 transition-transform duration-200 ease-in"
                  ></span>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-900 dark:text-white">Push Notifications</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">Receive alerts on your device</p>
                </div>
                <div className="relative inline-block w-10 mr-2 align-middle select-none">
                  <input 
                    type="checkbox" 
                    id="toggle-2" 
                    className="sr-only"
                  />
                  <label 
                    htmlFor="toggle-2" 
                    className="block h-6 w-10 rounded-full bg-gray-300 dark:bg-gray-600 cursor-pointer"
                  ></label>
                  <span 
                    className="absolute left-0 top-0 block h-6 w-6 transform translate-x-0 rounded-full bg-white border border-gray-300 transition-transform duration-200 ease-in"
                  ></span>
                </div>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Alerts;