import prisma from '../lib/prisma';
import { getPrices, batchUpdatePrices, getHistoricalPrice, EnhancedPriceData } from '../lib/cryptoApi';
import { TransactionType } from '../generated/prisma';

export interface PortfolioSummary {
  totalValue: number;
  totalCostBasis: number;
  totalUnrealizedPnL: number;
  totalRealizedPnL: number;
  totalPercentageChange: number;
  holdingsCount: number;
}

export interface TimeBasedPerformance {
  period: string;
  startValue: number;
  endValue: number;
  absoluteReturn: number;
  percentageReturn: number;
  startDate: Date;
  endDate: Date;
}

export interface AdvancedPortfolioMetrics {
  sharpeRatio: number | null;
  volatility: number | null;
  maxDrawdown: number | null;
  maxDrawdownPeriod: { start: Date; end: Date } | null;
  averageReturn: number;
  winRate: number;
  bestDay: { date: Date; return: number } | null;
  worstDay: { date: Date; return: number } | null;
  totalTradingDays: number;
}

export interface BenchmarkComparison {
  portfolioReturn: number;
  benchmarkReturn: number;
  alpha: number;
  beta: number | null;
  correlation: number | null;
  outperformance: number;
  benchmarkName: string;
}

export interface EnhancedPortfolioMetrics {
  summary: PortfolioSummary;
  timeBasedPerformance: TimeBasedPerformance[];
  advancedMetrics: AdvancedPortfolioMetrics;
  benchmarkComparisons: BenchmarkComparison[];
  riskMetrics: {
    valueAtRisk95: number | null;
    valueAtRisk99: number | null;
    conditionalValueAtRisk: number | null;
    downsideDeviation: number | null;
    sortinoRatio: number | null;
  };
}

export interface HoldingWithMetrics {
  id: string;
  userId: string;
  tokenSymbol: string;
  tokenName: string;
  currentAmount: number;
  averageCostBasis: number | null;
  totalCostBasis: number | null;
  currentPrice: number | null;
  currentValue: number | null;
  unrealizedPnL: number | null;
  realizedPnL: number | null;
  percentageChange: number | null;
  lastPriceUpdate: Date | null;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Calculate portfolio metrics for a holding based on its transactions
 */
export async function calculateHoldingMetrics(holdingId: string): Promise<void> {
  const holding = await prisma.portfolioHolding.findUnique({
    where: { id: holdingId },
    include: { transactions: true }
  });

  if (!holding) {
    throw new Error('Holding not found');
  }

  let totalCostBasis = 0;
  let totalAmount = 0;
  let realizedPnL = 0;

  // Calculate metrics from transactions
  for (const transaction of holding.transactions) {
    const amount = transaction.amount;
    const price = transaction.pricePerToken || 0;
    const value = transaction.totalValue || (amount * price);

    switch (transaction.type) {
      case TransactionType.BUY:
      case TransactionType.TRANSFER_IN:
      case TransactionType.AIRDROP:
      case TransactionType.REWARD:
        totalAmount += amount;
        totalCostBasis += value;
        break;

      case TransactionType.SELL:
      case TransactionType.TRANSFER_OUT:
        totalAmount -= amount;
        // Calculate realized P&L for sells
        if (transaction.type === TransactionType.SELL && totalCostBasis > 0) {
          const avgCostBasis = totalCostBasis / (totalAmount + amount); // Amount before this transaction
          const costOfSold = avgCostBasis * amount;
          realizedPnL += (value - costOfSold);
          totalCostBasis -= costOfSold;
        }
        break;

      case TransactionType.STAKE:
      case TransactionType.UNSTAKE:
        // For staking, we don't change the cost basis, just track the amount
        if (transaction.type === TransactionType.STAKE) {
          // Amount is locked but still owned
        } else {
          // Amount is unlocked
        }
        break;
    }
  }

  const averageCostBasis = totalAmount > 0 ? totalCostBasis / totalAmount : null;

  // Update the holding with calculated metrics
  await prisma.portfolioHolding.update({
    where: { id: holdingId },
    data: {
      currentAmount: totalAmount,
      averageCostBasis,
      totalCostBasis,
      realizedPnL,
      updatedAt: new Date()
    }
  });
}

/**
 * Update current prices and calculate unrealized P&L for holdings
 */
export async function updateHoldingPrices(userId: string, useRetry: boolean = true): Promise<{
  updated: number;
  failed: number;
  errors: string[];
}> {
  const holdings = await prisma.portfolioHolding.findMany({
    where: { userId }
  });

  if (holdings.length === 0) {
    return { updated: 0, failed: 0, errors: [] };
  }

  const tokenSymbols = holdings.map(h => h.tokenSymbol);
  const errors: string[] = [];
  let updated = 0;
  let failed = 0;

  try {
    // Use batch update with retry logic for better reliability
    const prices = useRetry
      ? await batchUpdatePrices(tokenSymbols, 3)
      : await getPrices(tokenSymbols);

    // Update each holding with current price and calculated values
    for (const holding of holdings) {
      try {
        const priceData = prices[holding.tokenSymbol.toLowerCase()];

        if (!priceData) {
          errors.push(`No price data found for ${holding.tokenSymbol}`);
          failed++;
          continue;
        }

        const currentPrice = priceData.usd;
        let currentValue = null;
        let unrealizedPnL = null;
        let percentageChange = null;

        if (currentPrice > 0 && holding.currentAmount > 0) {
          currentValue = holding.currentAmount * currentPrice;

          if (holding.totalCostBasis && holding.totalCostBasis > 0) {
            unrealizedPnL = currentValue - holding.totalCostBasis;
            percentageChange = (unrealizedPnL / holding.totalCostBasis) * 100;
          }
        }

        await prisma.portfolioHolding.update({
          where: { id: holding.id },
          data: {
            currentPrice: currentPrice > 0 ? currentPrice : null,
            currentValue,
            unrealizedPnL,
            percentageChange,
            lastPriceUpdate: new Date(),
            updatedAt: new Date()
          }
        });

        updated++;
      } catch (error) {
        const errorMsg = `Failed to update ${holding.tokenSymbol}: ${error}`;
        errors.push(errorMsg);
        console.error(errorMsg);
        failed++;
      }
    }
  } catch (error) {
    const errorMsg = `Failed to fetch prices: ${error}`;
    errors.push(errorMsg);
    console.error(errorMsg);
    failed = holdings.length;
  }

  return { updated, failed, errors };
}

/**
 * Get portfolio summary with aggregated metrics
 */
export async function getPortfolioSummary(userId: string): Promise<PortfolioSummary & {
  priceUpdateStats?: { updated: number; failed: number; errors: string[] };
}> {
  // First update prices and get update statistics
  const priceUpdateStats = await updateHoldingPrices(userId);

  const holdings = await prisma.portfolioHolding.findMany({
    where: { userId }
  });

  let totalValue = 0;
  let totalCostBasis = 0;
  let totalUnrealizedPnL = 0;
  let totalRealizedPnL = 0;

  for (const holding of holdings) {
    totalValue += holding.currentValue || 0;
    totalCostBasis += holding.totalCostBasis || 0;
    totalUnrealizedPnL += holding.unrealizedPnL || 0;
    totalRealizedPnL += holding.realizedPnL || 0;
  }

  const totalPercentageChange = totalCostBasis > 0
    ? (totalUnrealizedPnL / totalCostBasis) * 100
    : 0;

  return {
    totalValue,
    totalCostBasis,
    totalUnrealizedPnL,
    totalRealizedPnL,
    totalPercentageChange,
    holdingsCount: holdings.length,
    priceUpdateStats
  };
}

/**
 * Get holdings with updated metrics
 */
export async function getHoldingsWithMetrics(userId: string): Promise<HoldingWithMetrics[]> {
  // Update prices first
  await updateHoldingPrices(userId);

  const holdings = await prisma.portfolioHolding.findMany({
    where: { userId },
    orderBy: [
      { currentValue: 'desc' },
      { tokenSymbol: 'asc' }
    ]
  });

  return holdings;
}

/**
 * Add a new transaction and recalculate holding metrics
 */
export async function addTransaction(
  holdingId: string,
  transactionData: {
    type: TransactionType;
    amount: number;
    pricePerToken?: number;
    totalValue?: number;
    transactionFee?: number;
    feeTokenSymbol?: string;
    exchangeName?: string;
    transactionHash?: string;
    date: Date;
    notes?: string;
  }
): Promise<void> {
  // Create the transaction
  await prisma.transaction.create({
    data: {
      holdingId,
      ...transactionData,
      updatedAt: new Date()
    }
  });

  // Recalculate holding metrics
  await calculateHoldingMetrics(holdingId);
}

/**
 * Enhanced transaction creation with historical price lookup
 */
export async function addTransactionWithPriceLookup(
  holdingId: string,
  transactionData: {
    type: TransactionType;
    amount: number;
    pricePerToken?: number;
    totalValue?: number;
    transactionFee?: number;
    feeTokenSymbol?: string;
    exchangeName?: string;
    transactionHash?: string;
    date: Date;
    notes?: string;
  }
): Promise<{ success: boolean; historicalPrice?: number; message: string }> {
  try {
    // Get holding info for price lookup
    const holding = await prisma.portfolioHolding.findUnique({
      where: { id: holdingId }
    });

    if (!holding) {
      return { success: false, message: 'Holding not found' };
    }

    let finalTransactionData = { ...transactionData };

    // If no price provided, try to fetch historical price
    if (!transactionData.pricePerToken && !transactionData.totalValue) {
      console.log(`Attempting to fetch historical price for ${holding.tokenSymbol} on ${transactionData.date}`);

      const historicalPrice = await getHistoricalPrice(holding.tokenSymbol, transactionData.date);

      if (historicalPrice) {
        finalTransactionData.pricePerToken = historicalPrice;
        finalTransactionData.totalValue = historicalPrice * transactionData.amount;

        await addTransaction(holdingId, finalTransactionData);

        return {
          success: true,
          historicalPrice,
          message: `Transaction added with historical price: $${historicalPrice.toFixed(2)}`
        };
      } else {
        // Still create transaction without price data
        await addTransaction(holdingId, finalTransactionData);

        return {
          success: true,
          message: 'Transaction added without price data (historical price not available)'
        };
      }
    } else {
      // Create transaction with provided price data
      await addTransaction(holdingId, finalTransactionData);

      return {
        success: true,
        message: 'Transaction added successfully'
      };
    }
  } catch (error) {
    console.error('Error adding transaction with price lookup:', error);
    return {
      success: false,
      message: `Failed to add transaction: ${error}`
    };
  }
}

/**
 * Bulk price update for all users (useful for scheduled tasks)
 */
export async function bulkUpdateAllUserPrices(): Promise<{
  usersProcessed: number;
  totalHoldingsUpdated: number;
  totalErrors: number;
  processingTime: number;
}> {
  const startTime = Date.now();
  let usersProcessed = 0;
  let totalHoldingsUpdated = 0;
  let totalErrors = 0;

  try {
    // Get all users with holdings
    const usersWithHoldings = await prisma.user.findMany({
      where: {
        holdings: {
          some: {}
        }
      },
      select: { id: true }
    });

    console.log(`Starting bulk price update for ${usersWithHoldings.length} users`);

    for (const user of usersWithHoldings) {
      try {
        const result = await updateHoldingPrices(user.id, true);
        totalHoldingsUpdated += result.updated;
        totalErrors += result.failed;
        usersProcessed++;

        // Add small delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 100));
      } catch (error) {
        console.error(`Failed to update prices for user ${user.id}:`, error);
        totalErrors++;
      }
    }

    const processingTime = Date.now() - startTime;

    console.log(`Bulk price update completed: ${usersProcessed} users, ${totalHoldingsUpdated} holdings updated, ${totalErrors} errors, ${processingTime}ms`);

    return {
      usersProcessed,
      totalHoldingsUpdated,
      totalErrors,
      processingTime
    };
  } catch (error) {
    console.error('Error in bulk price update:', error);
    return {
      usersProcessed,
      totalHoldingsUpdated,
      totalErrors: totalErrors + 1,
      processingTime: Date.now() - startTime
    };
  }
}

/**
 * Get portfolio performance analytics
 */
export async function getPortfolioAnalytics(userId: string): Promise<{
  summary: PortfolioSummary;
  topPerformers: Array<{ symbol: string; percentageChange: number; value: number }>;
  worstPerformers: Array<{ symbol: string; percentageChange: number; value: number }>;
  allocationByValue: Array<{ symbol: string; percentage: number; value: number }>;
  recentActivity: Array<{ type: string; symbol: string; amount: number; date: Date }>;
}> {
  // Get updated portfolio summary
  const summary = await getPortfolioSummary(userId);

  // Get holdings with performance data
  const holdings = await getHoldingsWithMetrics(userId);

  // Get recent transactions
  const recentTransactions = await prisma.transaction.findMany({
    where: {
      holding: { userId }
    },
    include: {
      holding: {
        select: { tokenSymbol: true }
      }
    },
    orderBy: { date: 'desc' },
    take: 10
  });

  // Calculate top and worst performers
  const holdingsWithPerformance = holdings
    .filter(h => h.percentageChange !== null && h.currentValue && h.currentValue > 0)
    .map(h => ({
      symbol: h.tokenSymbol,
      percentageChange: h.percentageChange!,
      value: h.currentValue!
    }));

  const topPerformers = holdingsWithPerformance
    .sort((a, b) => b.percentageChange - a.percentageChange)
    .slice(0, 5);

  const worstPerformers = holdingsWithPerformance
    .sort((a, b) => a.percentageChange - b.percentageChange)
    .slice(0, 5);

  // Calculate allocation by value
  const totalPortfolioValue = holdings.reduce((sum, h) => sum + (h.currentValue || 0), 0);
  const allocationByValue = holdings
    .filter(h => h.currentValue && h.currentValue > 0)
    .map(h => ({
      symbol: h.tokenSymbol,
      value: h.currentValue!,
      percentage: totalPortfolioValue > 0 ? (h.currentValue! / totalPortfolioValue) * 100 : 0
    }))
    .sort((a, b) => b.value - a.value);

  // Format recent activity
  const recentActivity = recentTransactions.map(t => ({
    type: t.type,
    symbol: t.holding.tokenSymbol,
    amount: t.amount,
    date: t.date
  }));

  return {
    summary,
    topPerformers,
    worstPerformers,
    allocationByValue,
    recentActivity
  };
}

/**
 * Calculate time-based performance metrics for different periods
 */
export async function calculateTimeBasedPerformance(userId: string): Promise<TimeBasedPerformance[]> {
  const now = new Date();
  const periods = [
    { name: '1D', days: 1 },
    { name: '7D', days: 7 },
    { name: '30D', days: 30 },
    { name: '90D', days: 90 },
    { name: '1Y', days: 365 },
    { name: 'YTD', days: Math.floor((now.getTime() - new Date(now.getFullYear(), 0, 1).getTime()) / (1000 * 60 * 60 * 24)) }
  ];

  const results: TimeBasedPerformance[] = [];

  for (const period of periods) {
    const startDate = new Date(now.getTime() - (period.days * 24 * 60 * 60 * 1000));

    // Get portfolio value at start of period (estimated from transactions)
    const startValue = await estimatePortfolioValueAtDate(userId, startDate);

    // Get current portfolio value
    const currentSummary = await getPortfolioSummary(userId);
    const endValue = currentSummary.totalValue;

    const absoluteReturn = endValue - startValue;
    const percentageReturn = startValue > 0 ? (absoluteReturn / startValue) * 100 : 0;

    results.push({
      period: period.name,
      startValue,
      endValue,
      absoluteReturn,
      percentageReturn,
      startDate,
      endDate: now
    });
  }

  return results;
}

/**
 * Estimate portfolio value at a specific date based on transaction history
 */
async function estimatePortfolioValueAtDate(userId: string, targetDate: Date): Promise<number> {
  // Get all transactions up to the target date
  const transactions = await prisma.transaction.findMany({
    where: {
      holding: { userId },
      date: { lte: targetDate }
    },
    include: {
      holding: true
    },
    orderBy: { date: 'asc' }
  });

  // Calculate holdings at that date
  const holdingsAtDate: { [symbol: string]: { amount: number; costBasis: number } } = {};

  for (const transaction of transactions) {
    const symbol = transaction.holding.tokenSymbol;

    if (!holdingsAtDate[symbol]) {
      holdingsAtDate[symbol] = { amount: 0, costBasis: 0 };
    }

    const amount = transaction.amount;
    const value = transaction.totalValue || (transaction.pricePerToken || 0) * amount;

    switch (transaction.type) {
      case TransactionType.BUY:
      case TransactionType.TRANSFER_IN:
      case TransactionType.AIRDROP:
      case TransactionType.REWARD:
        holdingsAtDate[symbol].amount += amount;
        holdingsAtDate[symbol].costBasis += value;
        break;
      case TransactionType.SELL:
      case TransactionType.TRANSFER_OUT:
        holdingsAtDate[symbol].amount -= amount;
        // Proportionally reduce cost basis
        if (holdingsAtDate[symbol].amount > 0) {
          const ratio = amount / (holdingsAtDate[symbol].amount + amount);
          holdingsAtDate[symbol].costBasis *= (1 - ratio);
        }
        break;
    }
  }

  // Get historical prices for the target date and calculate value
  let totalValue = 0;

  for (const [symbol, holding] of Object.entries(holdingsAtDate)) {
    if (holding.amount > 0) {
      try {
        const historicalPrice = await getHistoricalPrice(symbol, targetDate);
        if (historicalPrice) {
          totalValue += holding.amount * historicalPrice;
        } else {
          // Fallback to cost basis if no historical price available
          totalValue += holding.costBasis;
        }
      } catch (error) {
        console.warn(`Failed to get historical price for ${symbol} at ${targetDate}:`, error);
        totalValue += holding.costBasis;
      }
    }
  }

  return totalValue;
}

/**
 * Calculate advanced portfolio metrics including Sharpe ratio, volatility, etc.
 */
export async function calculateAdvancedMetrics(userId: string): Promise<AdvancedPortfolioMetrics> {
  // Get daily portfolio values for the last year
  const dailyReturns = await calculateDailyReturns(userId, 365);

  if (dailyReturns.length === 0) {
    return {
      sharpeRatio: null,
      volatility: null,
      maxDrawdown: null,
      maxDrawdownPeriod: null,
      averageReturn: 0,
      winRate: 0,
      bestDay: null,
      worstDay: null,
      totalTradingDays: 0
    };
  }

  // Calculate basic statistics
  const returns = dailyReturns.map(d => d.return);
  const averageReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;
  const variance = returns.reduce((sum, r) => sum + Math.pow(r - averageReturn, 2), 0) / returns.length;
  const volatility = Math.sqrt(variance) * Math.sqrt(365); // Annualized volatility

  // Calculate Sharpe ratio (assuming 0% risk-free rate for crypto)
  const sharpeRatio = volatility > 0 ? (averageReturn * 365) / volatility : null;

  // Calculate max drawdown
  const { maxDrawdown, maxDrawdownPeriod } = calculateMaxDrawdown(dailyReturns);

  // Calculate win rate
  const winningDays = returns.filter(r => r > 0).length;
  const winRate = (winningDays / returns.length) * 100;

  // Find best and worst days
  const bestDayIndex = returns.indexOf(Math.max(...returns));
  const worstDayIndex = returns.indexOf(Math.min(...returns));

  const bestDay = bestDayIndex >= 0 ? {
    date: dailyReturns[bestDayIndex].date,
    return: returns[bestDayIndex]
  } : null;

  const worstDay = worstDayIndex >= 0 ? {
    date: dailyReturns[worstDayIndex].date,
    return: returns[worstDayIndex]
  } : null;

  return {
    sharpeRatio,
    volatility,
    maxDrawdown,
    maxDrawdownPeriod,
    averageReturn: averageReturn * 365, // Annualized
    winRate,
    bestDay,
    worstDay,
    totalTradingDays: returns.length
  };
}

/**
 * Calculate daily returns for portfolio over a specified period
 */
async function calculateDailyReturns(userId: string, days: number): Promise<Array<{ date: Date; value: number; return: number }>> {
  const endDate = new Date();
  const startDate = new Date(endDate.getTime() - (days * 24 * 60 * 60 * 1000));

  const dailyValues: Array<{ date: Date; value: number; return: number }> = [];
  let previousValue: number | null = null;

  // Calculate portfolio value for each day
  for (let i = 0; i < days; i++) {
    const currentDate = new Date(startDate.getTime() + (i * 24 * 60 * 60 * 1000));

    try {
      const portfolioValue = await estimatePortfolioValueAtDate(userId, currentDate);

      const dailyReturn = previousValue !== null && previousValue > 0
        ? ((portfolioValue - previousValue) / previousValue) * 100
        : 0;

      dailyValues.push({
        date: currentDate,
        value: portfolioValue,
        return: dailyReturn
      });

      previousValue = portfolioValue;
    } catch (error) {
      console.warn(`Failed to calculate portfolio value for ${currentDate}:`, error);
    }
  }

  return dailyValues;
}

/**
 * Calculate maximum drawdown and its period
 */
function calculateMaxDrawdown(dailyReturns: Array<{ date: Date; value: number; return: number }>): {
  maxDrawdown: number | null;
  maxDrawdownPeriod: { start: Date; end: Date } | null;
} {
  if (dailyReturns.length === 0) {
    return { maxDrawdown: null, maxDrawdownPeriod: null };
  }

  let maxDrawdown = 0;
  let maxDrawdownPeriod: { start: Date; end: Date } | null = null;
  let peak = dailyReturns[0].value;
  let peakDate = dailyReturns[0].date;

  for (const day of dailyReturns) {
    if (day.value > peak) {
      peak = day.value;
      peakDate = day.date;
    }

    const drawdown = peak > 0 ? ((peak - day.value) / peak) * 100 : 0;

    if (drawdown > maxDrawdown) {
      maxDrawdown = drawdown;
      maxDrawdownPeriod = { start: peakDate, end: day.date };
    }
  }

  return {
    maxDrawdown: maxDrawdown > 0 ? maxDrawdown : null,
    maxDrawdownPeriod
  };
}

/**
 * Calculate risk metrics including VaR and Sortino ratio
 */
export async function calculateRiskMetrics(userId: string): Promise<{
  valueAtRisk95: number | null;
  valueAtRisk99: number | null;
  conditionalValueAtRisk: number | null;
  downsideDeviation: number | null;
  sortinoRatio: number | null;
}> {
  const dailyReturns = await calculateDailyReturns(userId, 365);

  if (dailyReturns.length === 0) {
    return {
      valueAtRisk95: null,
      valueAtRisk99: null,
      conditionalValueAtRisk: null,
      downsideDeviation: null,
      sortinoRatio: null
    };
  }

  const returns = dailyReturns.map(d => d.return).sort((a, b) => a - b);
  const averageReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;

  // Calculate Value at Risk (VaR)
  const var95Index = Math.floor(returns.length * 0.05);
  const var99Index = Math.floor(returns.length * 0.01);

  const valueAtRisk95 = returns[var95Index] || null;
  const valueAtRisk99 = returns[var99Index] || null;

  // Calculate Conditional Value at Risk (CVaR) - average of worst 5% returns
  const worstReturns = returns.slice(0, var95Index + 1);
  const conditionalValueAtRisk = worstReturns.length > 0
    ? worstReturns.reduce((sum, r) => sum + r, 0) / worstReturns.length
    : null;

  // Calculate downside deviation (only negative returns)
  const negativeReturns = returns.filter(r => r < 0);
  const downsideDeviation = negativeReturns.length > 0
    ? Math.sqrt(negativeReturns.reduce((sum, r) => sum + Math.pow(r, 2), 0) / negativeReturns.length)
    : null;

  // Calculate Sortino ratio
  const sortinoRatio = downsideDeviation && downsideDeviation > 0
    ? (averageReturn * 365) / (downsideDeviation * Math.sqrt(365))
    : null;

  return {
    valueAtRisk95,
    valueAtRisk99,
    conditionalValueAtRisk,
    downsideDeviation,
    sortinoRatio
  };
}

/**
 * Calculate benchmark comparisons (vs BTC, ETH, etc.)
 */
export async function calculateBenchmarkComparisons(userId: string): Promise<BenchmarkComparison[]> {
  const benchmarks = ['BTC', 'ETH'];
  const comparisons: BenchmarkComparison[] = [];

  // Get portfolio performance for the last year
  const portfolioReturns = await calculateDailyReturns(userId, 365);

  if (portfolioReturns.length === 0) {
    return comparisons;
  }

  const portfolioReturn = portfolioReturns.length > 0
    ? ((portfolioReturns[portfolioReturns.length - 1].value - portfolioReturns[0].value) / portfolioReturns[0].value) * 100
    : 0;

  for (const benchmark of benchmarks) {
    try {
      // Calculate benchmark return for the same period
      const startDate = portfolioReturns[0].date;
      const endDate = portfolioReturns[portfolioReturns.length - 1].date;

      const startPrice = await getHistoricalPrice(benchmark, startDate);
      const endPrice = await getHistoricalPrice(benchmark, endDate);

      if (startPrice && endPrice && startPrice > 0) {
        const benchmarkReturn = ((endPrice - startPrice) / startPrice) * 100;
        const outperformance = portfolioReturn - benchmarkReturn;

        // Calculate correlation and beta (simplified)
        const { correlation, beta } = calculateCorrelationAndBeta(portfolioReturns, benchmark);

        const alpha = portfolioReturn - (benchmarkReturn * (beta || 1));

        comparisons.push({
          portfolioReturn,
          benchmarkReturn,
          alpha,
          beta,
          correlation,
          outperformance,
          benchmarkName: benchmark
        });
      }
    } catch (error) {
      console.warn(`Failed to calculate benchmark comparison for ${benchmark}:`, error);
    }
  }

  return comparisons;
}

/**
 * Calculate correlation and beta between portfolio and benchmark (simplified)
 */
function calculateCorrelationAndBeta(portfolioReturns: Array<{ date: Date; value: number; return: number }>, benchmark: string): {
  correlation: number | null;
  beta: number | null;
} {
  // This is a simplified implementation
  // In a real-world scenario, you'd need daily benchmark prices to calculate proper correlation and beta
  return {
    correlation: null, // Would need benchmark daily returns
    beta: null // Would need benchmark daily returns
  };
}

/**
 * Get comprehensive enhanced portfolio metrics
 */
export async function getEnhancedPortfolioMetrics(userId: string): Promise<EnhancedPortfolioMetrics> {
  try {
    // Run all calculations in parallel for better performance
    const [
      summary,
      timeBasedPerformance,
      advancedMetrics,
      riskMetrics,
      benchmarkComparisons
    ] = await Promise.all([
      getPortfolioSummary(userId),
      calculateTimeBasedPerformance(userId),
      calculateAdvancedMetrics(userId),
      calculateRiskMetrics(userId),
      calculateBenchmarkComparisons(userId)
    ]);

    return {
      summary,
      timeBasedPerformance,
      advancedMetrics,
      benchmarkComparisons,
      riskMetrics
    };
  } catch (error) {
    console.error('Error calculating enhanced portfolio metrics:', error);

    // Return fallback data if calculations fail
    const summary = await getPortfolioSummary(userId);

    return {
      summary,
      timeBasedPerformance: [],
      advancedMetrics: {
        sharpeRatio: null,
        volatility: null,
        maxDrawdown: null,
        maxDrawdownPeriod: null,
        averageReturn: 0,
        winRate: 0,
        bestDay: null,
        worstDay: null,
        totalTradingDays: 0
      },
      benchmarkComparisons: [],
      riskMetrics: {
        valueAtRisk95: null,
        valueAtRisk99: null,
        conditionalValueAtRisk: null,
        downsideDeviation: null,
        sortinoRatio: null
      }
    };
  }
}
