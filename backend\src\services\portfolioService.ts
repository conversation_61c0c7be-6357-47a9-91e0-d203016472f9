import prisma from '../lib/prisma';
import { getPrices, batchUpdatePrices, getHistoricalPrice, EnhancedPriceData } from '../lib/cryptoApi';
import { TransactionType } from '../generated/prisma';

export interface PortfolioSummary {
  totalValue: number;
  totalCostBasis: number;
  totalUnrealizedPnL: number;
  totalRealizedPnL: number;
  totalPercentageChange: number;
  holdingsCount: number;
}

export interface HoldingWithMetrics {
  id: string;
  userId: string;
  tokenSymbol: string;
  tokenName: string;
  currentAmount: number;
  averageCostBasis: number | null;
  totalCostBasis: number | null;
  currentPrice: number | null;
  currentValue: number | null;
  unrealizedPnL: number | null;
  realizedPnL: number | null;
  percentageChange: number | null;
  lastPriceUpdate: Date | null;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Calculate portfolio metrics for a holding based on its transactions
 */
export async function calculateHoldingMetrics(holdingId: string): Promise<void> {
  const holding = await prisma.portfolioHolding.findUnique({
    where: { id: holdingId },
    include: { transactions: true }
  });

  if (!holding) {
    throw new Error('Holding not found');
  }

  let totalCostBasis = 0;
  let totalAmount = 0;
  let realizedPnL = 0;

  // Calculate metrics from transactions
  for (const transaction of holding.transactions) {
    const amount = transaction.amount;
    const price = transaction.pricePerToken || 0;
    const value = transaction.totalValue || (amount * price);

    switch (transaction.type) {
      case TransactionType.BUY:
      case TransactionType.TRANSFER_IN:
      case TransactionType.AIRDROP:
      case TransactionType.REWARD:
        totalAmount += amount;
        totalCostBasis += value;
        break;

      case TransactionType.SELL:
      case TransactionType.TRANSFER_OUT:
        totalAmount -= amount;
        // Calculate realized P&L for sells
        if (transaction.type === TransactionType.SELL && totalCostBasis > 0) {
          const avgCostBasis = totalCostBasis / (totalAmount + amount); // Amount before this transaction
          const costOfSold = avgCostBasis * amount;
          realizedPnL += (value - costOfSold);
          totalCostBasis -= costOfSold;
        }
        break;

      case TransactionType.STAKE:
      case TransactionType.UNSTAKE:
        // For staking, we don't change the cost basis, just track the amount
        if (transaction.type === TransactionType.STAKE) {
          // Amount is locked but still owned
        } else {
          // Amount is unlocked
        }
        break;
    }
  }

  const averageCostBasis = totalAmount > 0 ? totalCostBasis / totalAmount : null;

  // Update the holding with calculated metrics
  await prisma.portfolioHolding.update({
    where: { id: holdingId },
    data: {
      currentAmount: totalAmount,
      averageCostBasis,
      totalCostBasis,
      realizedPnL,
      updatedAt: new Date()
    }
  });
}

/**
 * Update current prices and calculate unrealized P&L for holdings
 */
export async function updateHoldingPrices(userId: string, useRetry: boolean = true): Promise<{
  updated: number;
  failed: number;
  errors: string[];
}> {
  const holdings = await prisma.portfolioHolding.findMany({
    where: { userId }
  });

  if (holdings.length === 0) {
    return { updated: 0, failed: 0, errors: [] };
  }

  const tokenSymbols = holdings.map(h => h.tokenSymbol);
  const errors: string[] = [];
  let updated = 0;
  let failed = 0;

  try {
    // Use batch update with retry logic for better reliability
    const prices = useRetry
      ? await batchUpdatePrices(tokenSymbols, 3)
      : await getPrices(tokenSymbols);

    // Update each holding with current price and calculated values
    for (const holding of holdings) {
      try {
        const priceData = prices[holding.tokenSymbol.toLowerCase()];

        if (!priceData) {
          errors.push(`No price data found for ${holding.tokenSymbol}`);
          failed++;
          continue;
        }

        const currentPrice = priceData.usd;
        let currentValue = null;
        let unrealizedPnL = null;
        let percentageChange = null;

        if (currentPrice > 0 && holding.currentAmount > 0) {
          currentValue = holding.currentAmount * currentPrice;

          if (holding.totalCostBasis && holding.totalCostBasis > 0) {
            unrealizedPnL = currentValue - holding.totalCostBasis;
            percentageChange = (unrealizedPnL / holding.totalCostBasis) * 100;
          }
        }

        await prisma.portfolioHolding.update({
          where: { id: holding.id },
          data: {
            currentPrice: currentPrice > 0 ? currentPrice : null,
            currentValue,
            unrealizedPnL,
            percentageChange,
            lastPriceUpdate: new Date(),
            updatedAt: new Date()
          }
        });

        updated++;
      } catch (error) {
        const errorMsg = `Failed to update ${holding.tokenSymbol}: ${error}`;
        errors.push(errorMsg);
        console.error(errorMsg);
        failed++;
      }
    }
  } catch (error) {
    const errorMsg = `Failed to fetch prices: ${error}`;
    errors.push(errorMsg);
    console.error(errorMsg);
    failed = holdings.length;
  }

  return { updated, failed, errors };
}

/**
 * Get portfolio summary with aggregated metrics
 */
export async function getPortfolioSummary(userId: string): Promise<PortfolioSummary & {
  priceUpdateStats?: { updated: number; failed: number; errors: string[] };
}> {
  // First update prices and get update statistics
  const priceUpdateStats = await updateHoldingPrices(userId);

  const holdings = await prisma.portfolioHolding.findMany({
    where: { userId }
  });

  let totalValue = 0;
  let totalCostBasis = 0;
  let totalUnrealizedPnL = 0;
  let totalRealizedPnL = 0;

  for (const holding of holdings) {
    totalValue += holding.currentValue || 0;
    totalCostBasis += holding.totalCostBasis || 0;
    totalUnrealizedPnL += holding.unrealizedPnL || 0;
    totalRealizedPnL += holding.realizedPnL || 0;
  }

  const totalPercentageChange = totalCostBasis > 0
    ? (totalUnrealizedPnL / totalCostBasis) * 100
    : 0;

  return {
    totalValue,
    totalCostBasis,
    totalUnrealizedPnL,
    totalRealizedPnL,
    totalPercentageChange,
    holdingsCount: holdings.length,
    priceUpdateStats
  };
}

/**
 * Get holdings with updated metrics
 */
export async function getHoldingsWithMetrics(userId: string): Promise<HoldingWithMetrics[]> {
  // Update prices first
  await updateHoldingPrices(userId);

  const holdings = await prisma.portfolioHolding.findMany({
    where: { userId },
    orderBy: [
      { currentValue: 'desc' },
      { tokenSymbol: 'asc' }
    ]
  });

  return holdings;
}

/**
 * Add a new transaction and recalculate holding metrics
 */
export async function addTransaction(
  holdingId: string,
  transactionData: {
    type: TransactionType;
    amount: number;
    pricePerToken?: number;
    totalValue?: number;
    transactionFee?: number;
    feeTokenSymbol?: string;
    exchangeName?: string;
    transactionHash?: string;
    date: Date;
    notes?: string;
  }
): Promise<void> {
  // Create the transaction
  await prisma.transaction.create({
    data: {
      holdingId,
      ...transactionData,
      updatedAt: new Date()
    }
  });

  // Recalculate holding metrics
  await calculateHoldingMetrics(holdingId);
}

/**
 * Enhanced transaction creation with historical price lookup
 */
export async function addTransactionWithPriceLookup(
  holdingId: string,
  transactionData: {
    type: TransactionType;
    amount: number;
    pricePerToken?: number;
    totalValue?: number;
    transactionFee?: number;
    feeTokenSymbol?: string;
    exchangeName?: string;
    transactionHash?: string;
    date: Date;
    notes?: string;
  }
): Promise<{ success: boolean; historicalPrice?: number; message: string }> {
  try {
    // Get holding info for price lookup
    const holding = await prisma.portfolioHolding.findUnique({
      where: { id: holdingId }
    });

    if (!holding) {
      return { success: false, message: 'Holding not found' };
    }

    let finalTransactionData = { ...transactionData };

    // If no price provided, try to fetch historical price
    if (!transactionData.pricePerToken && !transactionData.totalValue) {
      console.log(`Attempting to fetch historical price for ${holding.tokenSymbol} on ${transactionData.date}`);

      const historicalPrice = await getHistoricalPrice(holding.tokenSymbol, transactionData.date);

      if (historicalPrice) {
        finalTransactionData.pricePerToken = historicalPrice;
        finalTransactionData.totalValue = historicalPrice * transactionData.amount;

        await addTransaction(holdingId, finalTransactionData);

        return {
          success: true,
          historicalPrice,
          message: `Transaction added with historical price: $${historicalPrice.toFixed(2)}`
        };
      } else {
        // Still create transaction without price data
        await addTransaction(holdingId, finalTransactionData);

        return {
          success: true,
          message: 'Transaction added without price data (historical price not available)'
        };
      }
    } else {
      // Create transaction with provided price data
      await addTransaction(holdingId, finalTransactionData);

      return {
        success: true,
        message: 'Transaction added successfully'
      };
    }
  } catch (error) {
    console.error('Error adding transaction with price lookup:', error);
    return {
      success: false,
      message: `Failed to add transaction: ${error}`
    };
  }
}

/**
 * Bulk price update for all users (useful for scheduled tasks)
 */
export async function bulkUpdateAllUserPrices(): Promise<{
  usersProcessed: number;
  totalHoldingsUpdated: number;
  totalErrors: number;
  processingTime: number;
}> {
  const startTime = Date.now();
  let usersProcessed = 0;
  let totalHoldingsUpdated = 0;
  let totalErrors = 0;

  try {
    // Get all users with holdings
    const usersWithHoldings = await prisma.user.findMany({
      where: {
        holdings: {
          some: {}
        }
      },
      select: { id: true }
    });

    console.log(`Starting bulk price update for ${usersWithHoldings.length} users`);

    for (const user of usersWithHoldings) {
      try {
        const result = await updateHoldingPrices(user.id, true);
        totalHoldingsUpdated += result.updated;
        totalErrors += result.failed;
        usersProcessed++;

        // Add small delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 100));
      } catch (error) {
        console.error(`Failed to update prices for user ${user.id}:`, error);
        totalErrors++;
      }
    }

    const processingTime = Date.now() - startTime;

    console.log(`Bulk price update completed: ${usersProcessed} users, ${totalHoldingsUpdated} holdings updated, ${totalErrors} errors, ${processingTime}ms`);

    return {
      usersProcessed,
      totalHoldingsUpdated,
      totalErrors,
      processingTime
    };
  } catch (error) {
    console.error('Error in bulk price update:', error);
    return {
      usersProcessed,
      totalHoldingsUpdated,
      totalErrors: totalErrors + 1,
      processingTime: Date.now() - startTime
    };
  }
}

/**
 * Get portfolio performance analytics
 */
export async function getPortfolioAnalytics(userId: string): Promise<{
  summary: PortfolioSummary;
  topPerformers: Array<{ symbol: string; percentageChange: number; value: number }>;
  worstPerformers: Array<{ symbol: string; percentageChange: number; value: number }>;
  allocationByValue: Array<{ symbol: string; percentage: number; value: number }>;
  recentActivity: Array<{ type: string; symbol: string; amount: number; date: Date }>;
}> {
  // Get updated portfolio summary
  const summary = await getPortfolioSummary(userId);

  // Get holdings with performance data
  const holdings = await getHoldingsWithMetrics(userId);

  // Get recent transactions
  const recentTransactions = await prisma.transaction.findMany({
    where: {
      holding: { userId }
    },
    include: {
      holding: {
        select: { tokenSymbol: true }
      }
    },
    orderBy: { date: 'desc' },
    take: 10
  });

  // Calculate top and worst performers
  const holdingsWithPerformance = holdings
    .filter(h => h.percentageChange !== null && h.currentValue && h.currentValue > 0)
    .map(h => ({
      symbol: h.tokenSymbol,
      percentageChange: h.percentageChange!,
      value: h.currentValue!
    }));

  const topPerformers = holdingsWithPerformance
    .sort((a, b) => b.percentageChange - a.percentageChange)
    .slice(0, 5);

  const worstPerformers = holdingsWithPerformance
    .sort((a, b) => a.percentageChange - b.percentageChange)
    .slice(0, 5);

  // Calculate allocation by value
  const totalPortfolioValue = holdings.reduce((sum, h) => sum + (h.currentValue || 0), 0);
  const allocationByValue = holdings
    .filter(h => h.currentValue && h.currentValue > 0)
    .map(h => ({
      symbol: h.tokenSymbol,
      value: h.currentValue!,
      percentage: totalPortfolioValue > 0 ? (h.currentValue! / totalPortfolioValue) * 100 : 0
    }))
    .sort((a, b) => b.value - a.value);

  // Format recent activity
  const recentActivity = recentTransactions.map(t => ({
    type: t.type,
    symbol: t.holding.tokenSymbol,
    amount: t.amount,
    date: t.date
  }));

  return {
    summary,
    topPerformers,
    worstPerformers,
    allocationByValue,
    recentActivity
  };
}
