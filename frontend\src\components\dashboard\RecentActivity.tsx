import React from 'react';
import Card from '../ui/Card';
import Badge from '../ui/Badge';
import { Clock, DollarSign, Gift, Bell } from 'lucide-react';
import { mockAlerts, mockAirdrops } from '../../data/mockData';

const RecentActivity: React.FC = () => {
  // Combine alerts and airdrops into a single activity feed
  const alerts = mockAlerts.map(alert => ({
    id: `alert-${alert.id}`,
    type: 'alert',
    title: alert.title,
    description: alert.description,
    timestamp: alert.timestamp,
    priority: alert.priority
  }));
  
  const airdrops = mockAirdrops
    .filter(airdrop => airdrop.status === 'active' || airdrop.status === 'upcoming')
    .map(airdrop => ({
      id: `airdrop-${airdrop.id}`,
      type: 'airdrop',
      title: `${airdrop.name} airdrop ${airdrop.status === 'active' ? 'active' : 'upcoming'}`,
      description: airdrop.description,
      timestamp: new Date(airdrop.deadline).getTime(),
      priority: airdrop.status === 'active' ? 'high' : 'medium' as 'high' | 'medium' | 'low'
    }));
  
  const activities = [...alerts, ...airdrops]
    .sort((a, b) => b.timestamp - a.timestamp)
    .slice(0, 5);
  
  return (
    <Card title="Recent Activity">
      <div className="divide-y divide-gray-200 dark:divide-gray-700">
        {activities.map(activity => (
          <div key={activity.id} className="p-4 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors">
            <div className="flex items-start">
              <div className="flex-shrink-0 mr-4">
                {activity.type === 'alert' ? (
                  <Bell className="h-5 w-5 text-indigo-500 dark:text-indigo-400" />
                ) : (
                  <Gift className="h-5 w-5 text-orange-500 dark:text-orange-400" />
                )}
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex justify-between">
                  <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                    {activity.title}
                  </p>
                  <Badge 
                    variant={
                      activity.priority === 'high' ? 'error' : 
                      activity.priority === 'medium' ? 'warning' : 
                      'default'
                    }
                    size="sm"
                    className="ml-2"
                  >
                    {activity.priority}
                  </Badge>
                </div>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400 line-clamp-2">
                  {activity.description}
                </p>
                <div className="mt-2 flex items-center text-xs text-gray-500 dark:text-gray-400">
                  <Clock className="h-3 w-3 mr-1" />
                  <span>{formatTimeAgo(activity.timestamp)}</span>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </Card>
  );
};

// Helper function to format timestamp as relative time
function formatTimeAgo(timestamp: number): string {
  const seconds = Math.floor((Date.now() - timestamp) / 1000);
  
  let interval = Math.floor(seconds / 31536000);
  if (interval >= 1) {
    return `${interval} year${interval === 1 ? '' : 's'} ago`;
  }
  
  interval = Math.floor(seconds / 2592000);
  if (interval >= 1) {
    return `${interval} month${interval === 1 ? '' : 's'} ago`;
  }
  
  interval = Math.floor(seconds / 86400);
  if (interval >= 1) {
    return `${interval} day${interval === 1 ? '' : 's'} ago`;
  }
  
  interval = Math.floor(seconds / 3600);
  if (interval >= 1) {
    return `${interval} hour${interval === 1 ? '' : 's'} ago`;
  }
  
  interval = Math.floor(seconds / 60);
  if (interval >= 1) {
    return `${interval} minute${interval === 1 ? '' : 's'} ago`;
  }
  
  return 'just now';
}

export default RecentActivity;