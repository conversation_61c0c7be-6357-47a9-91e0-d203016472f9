import { FastifyInstance } from 'fastify';
import prisma from '../lib/prisma';

export default async function (fastify: FastifyInstance) {
  // Get user's alerts
  fastify.get('/users/:userId', async (request, reply) => {
    const { userId } = request.params as { userId: string };
    
    try {
      const alerts = await prisma.alert.findMany({
        where: { userId }
      });
      
      return { alerts };
    } catch (error) {
      console.error('Error fetching alerts:', error);
      throw new Error('Failed to fetch alerts');
    }
  });

  // Get specific alert
  fastify.get('/:id', async (request, reply) => {
    const { id } = request.params as { id: string };
    
    try {
      const alert = await prisma.alert.findUnique({
        where: { id }
      });
      
      if (!alert) {
        reply.code(404).send({ error: 'Alert not found' });
        return;
      }
      
      return { alert };
    } catch (error) {
      console.error('Error fetching alert:', error);
      throw new Error('Failed to fetch alert');
    }
  });

  // Create alert
  fastify.post('/', async (request, reply) => {
    const { 
      userId, 
      type, 
      condition, 
      threshold, 
      tokenSymbol, 
      airdropId 
    } = request.body as { 
      userId: string; 
      type: string; 
      condition: string; 
      threshold?: number; 
      tokenSymbol?: string; 
      airdropId?: string;
    };
    
    try {
      const alert = await prisma.alert.create({
        data: {
          userId,
          type,
          condition,
          threshold,
          tokenSymbol,
          airdropId,
          active: true
        }
      });
      
      reply.code(201).send({ alert });
    } catch (error) {
      console.error('Error creating alert:', error);
      throw new Error('Failed to create alert');
    }
  });

  // Update alert
  fastify.put('/:id', async (request, reply) => {
    const { id } = request.params as { id: string };
    const { 
      condition, 
      threshold, 
      active 
    } = request.body as { 
      condition?: string; 
      threshold?: number; 
      active?: boolean;
    };
    
    try {
      const alert = await prisma.alert.update({
        where: { id },
        data: {
          ...(condition && { condition }),
          ...(threshold !== undefined && { threshold }),
          ...(active !== undefined && { active })
        }
      });
      
      return { alert };
    } catch (error) {
      console.error('Error updating alert:', error);
      throw new Error('Failed to update alert');
    }
  });

  // Delete alert
  fastify.delete('/:id', async (request, reply) => {
    const { id } = request.params as { id: string };
    
    try {
      await prisma.alert.delete({
        where: { id }
      });
      
      reply.code(204).send();
    } catch (error) {
      console.error('Error deleting alert:', error);
      throw new Error('Failed to delete alert');
    }
  });

  // Toggle alert active status
  fastify.put('/:id/toggle', async (request, reply) => {
    const { id } = request.params as { id: string };
    
    try {
      const alert = await prisma.alert.findUnique({
        where: { id }
      });
      
      if (!alert) {
        reply.code(404).send({ error: 'Alert not found' });
        return;
      }
      
      const updatedAlert = await prisma.alert.update({
        where: { id },
        data: { active: !alert.active }
      });
      
      return { alert: updatedAlert };
    } catch (error) {
      console.error('Error toggling alert:', error);
      throw new Error('Failed to toggle alert');
    }
  });

  // Mark alert as triggered
  fastify.put('/:id/trigger', async (request, reply) => {
    const { id } = request.params as { id: string };
    
    try {
      const alert = await prisma.alert.update({
        where: { id },
        data: { lastTriggered: new Date() }
      });
      
      return { alert };
    } catch (error) {
      console.error('Error marking alert as triggered:', error);
      throw new Error('Failed to mark alert as triggered');
    }
  });
}